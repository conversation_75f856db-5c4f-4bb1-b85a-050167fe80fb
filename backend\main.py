import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from typing import List

app = FastAPI(
    title="CutRequestStudio API",
    version="1.0.0"
)

# Configura CORS per accettare richieste dal frontend
# Leggi le origini da una variabile d'ambiente, separate da virgola
# Esempio: CORS_ORIGINS="http://localhost:3000,http://localhost:9002"
CORS_ORIGINS_STR = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:9002,http://localhost:8000,http://127.0.0.1:3000")
origins: List[str] = [origin.strip() for origin in CORS_ORIGINS_STR.split(',')]

# Aggiungi un controllo per assicurarti che origins non sia una lista vuota se CORS_ORIGINS_STR è vuoto
if not origins or not origins[0]: # Se la stringa era vuota o solo spazi/virgole
    origins = ["http://localhost:3000"] # Default a un valore sicuro se non configurato

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health_check():
    return {"status": "ok"}

from .routers import user
from .routers import request
from .routers import tire
from .routers import request_detail_cut
from .routers import cut_processing

# New simplified routers
from .routers import tires_enhanced
from .routers import request_items
from .routers import cut_operations

# Create API v1 router
from fastapi import APIRouter
api_v1 = APIRouter(prefix="/api/v1")

# Include all routers under /api/v1
api_v1.include_router(user.router)
api_v1.include_router(request.router)
api_v1.include_router(tire.router)
api_v1.include_router(request_detail_cut.router)
api_v1.include_router(cut_processing.router)

# Include new simplified routers
api_v1.include_router(tires_enhanced.router)
api_v1.include_router(request_items.router)
api_v1.include_router(cut_operations.router)

# Include the versioned API router in the main app
app.include_router(api_v1)
