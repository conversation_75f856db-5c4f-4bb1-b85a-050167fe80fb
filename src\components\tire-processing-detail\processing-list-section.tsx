"use client";

import * as React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area";
import { ArrowLeft, PlusCircle, Trash2 } from "lucide-react";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import type { TireProcessingItem } from "@/types";
import { cn } from "@/lib/utils";

interface ProcessingListSectionProps {
  processings: TireProcessingItem[];
  selectedProcessingId: string | null;
  onRowClick: (id: string) => void;
  onAction: (action: "back" | "add" | "delete") => void;
  onTableInputChange: (processingId: string, field: keyof TireProcessingItem, value: any) => void;
}

export function ProcessingListSection({
  processings,
  selectedProcessingId,
  onRowClick,
  onAction,
  onTableInputChange,
}: ProcessingListSectionProps) {

  const handleCellInputChange = (
    processingId: string,
    field: keyof TireProcessingItem,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value: string | number = e.target.value;
    // Only handle the 'n' field since other fields are now read-only
    if (field === 'n') {
      const numValue = parseInt(value, 10);
      value = isNaN(numValue) ? 0 : numValue;
    }
    onTableInputChange(processingId, field, value);
  };

  return (
    <section aria-labelledby="processing-list-heading" className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 id="processing-list-heading" className="text-xl font-semibold">
          Associated Processings
        </h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => onAction("back")} aria-label="Go back">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => onAction("add")} className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700" aria-label="Add new processing">
            <PlusCircle className="h-5 w-5" />
          </Button>
          <DeleteButton
            onClick={() => onAction("delete")}
            disabled={!selectedProcessingId}
            title="Confirm Processing Deletion"
            description="Are you sure you want to delete this processing? This action cannot be undone."
            itemType="processing"
          />
        </div>
      </div>
      <ScrollArea className="rounded-md h-72 bg-card">
        <Table>
          <TableHeader className="sticky top-0 bg-card z-10">
            <TableRow>
              <TableHead>Description 1</TableHead>
              <TableHead>Description 2</TableHead>
              <TableHead className="w-[100px]">Price</TableHead>
              <TableHead>Tyre Type</TableHead>
              <TableHead className="w-[120px]">Code1</TableHead>
              <TableHead className="w-[120px]">Code2</TableHead>
              <TableHead className="w-[80px]">N°</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {processings.map((proc) => (
              <TableRow
                key={proc.id}
                data-state={selectedProcessingId === proc.id ? "selected" : ""}
                onClick={() => onRowClick(proc.id)}
                className={cn(
                  "cursor-pointer",
                  selectedProcessingId === proc.id ? "bg-primary/20 hover:bg-primary/30" : "hover:bg-muted/50"
                )}
                aria-selected={selectedProcessingId === proc.id}
              >
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-muted/50 rounded-md">
                    {proc.description1}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-muted/50 rounded-md">
                    {proc.description2}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center justify-end text-xs px-3 py-2 bg-muted/50 rounded-md">
                    {proc.price}
                  </div>
                </TableCell>
                <TableCell>{proc.tyreType}</TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-muted/50 rounded-md">
                    {proc.code1}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-8 flex items-center text-xs px-3 py-2 bg-muted/50 rounded-md">
                    {proc.code2}
                  </div>
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    value={proc.n}
                    onChange={(e) => handleCellInputChange(proc.id, 'n', e)}
                    className="h-8 text-xs text-right bg-background/50"
                    onClick={(e) => e.stopPropagation()}
                  />
                </TableCell>
              </TableRow>
            ))}
            {processings.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center h-24">
                  No processings associated with this tire.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </ScrollArea>
    </section>
  );
}
