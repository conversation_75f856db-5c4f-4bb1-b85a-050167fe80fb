"use client";

import * as React from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, Database, Copy, ListFilter, Settings } from "lucide-react";
import type { Tire, DialogTire } from "@/types";
import { cn } from "@/lib/utils";
import { TireManagementDialog } from "@/components/tire-management/tire-management-dialog";
import { TireManagementButton } from "@/components/tire-management/tire-management-button";
import { DeleteButton } from "@/components/ui/delete-confirmation-dialog";
import { useToast } from "@/hooks/use-toast";
import { deleteRequestDetail } from "@/services/requestDetailService";

interface TiresSectionProps {
  tires: Tire[];
  selectedTireId: string | null;
  onRowClick: (id: string) => void;
  onProcessingType: () => void;
  onCopyTire: () => void;
  onManageCuts?: (tire: Tire) => void;
  processingNSums: Record<string, number>;
  onUpdateProcessingSums: (updater: (prev: Record<string, number>) => Record<string, number>) => void;
  onUpdateTires: (updater: (prev: Tire[]) => Tire[]) => void;
}

const DispositionBadge = ({ disposition }: { disposition: Tire["disposition"] }) => {
  let variant: "default" | "secondary" | "destructive" | "outline" | "accent" = "outline";
  switch (disposition) {
    case "AVAILABLE": variant = "default"; break;
    case "SCRAP": variant = "destructive"; break;
    case "TESTED": variant = "accent"; break;
    case "REPAIR": variant = "secondary"; break;
    default: variant = "outline";
  }
  return <Badge variant={variant} className={cn( variant === "accent" ? "bg-accent text-accent-foreground" : "")}>{disposition}</Badge>;
};


export function TiresSection({
  tires,
  selectedTireId,
  onRowClick,
  onProcessingType,
  onCopyTire,
  onManageCuts,
  processingNSums,
  onUpdateProcessingSums,
  onUpdateTires
}: TiresSectionProps) {
  const [isTireManagementDialogOpen, setIsTireManagementDialogOpen] = React.useState(false);
  const { toast } = useToast();

  const handleAddTiresFromManagement = (selectedTires: DialogTire[]) => {
    if (selectedTires.length === 0) return;

    // Create new tire entries from the selected dialog tires
    const newTires: Tire[] = selectedTires.map((dialogTire, index) => ({
      id: `MGT-${Date.now()}-${index}`,
      tugNo: dialogTire.tugNo || "-",
      section: "FROM_MANAGEMENT",
      projectNo: dialogTire.projectNo || "-",
      specNo: dialogTire.specNo || "-",
      tireSize: dialogTire.size || "-",
      pattern: dialogTire.pattern || "-",
      note: `Added via management. Owner: ${dialogTire.owner || "-"}, LI: ${dialogTire.loadIndex || "-"}, Loc: ${dialogTire.location || "-"}`,
      disposition: "AVAILABLE",
      quantity: 1,
    }));

    // Add the new tires to the existing tires array
    onUpdateTires(prev => [...prev, ...newTires]);

    // Initialize processing sums for the new tires
    const newSums = newTires.reduce((acc, tire) => {
      acc[tire.id] = 0;
      return acc;
    }, {} as Record<string, number>);
    // Add the new sums to the existing sums
    processingNSums && onUpdateProcessingSums((prevSums: Record<string, number>) => ({ ...prevSums, ...newSums }));

    // Close the dialog
    setIsTireManagementDialogOpen(false);
  };

  // Handle tire deletion
  const handleDeleteTire = async (tireId: string) => {
    try {
      // Call the API to delete the tire
      await deleteRequestDetail(tireId);

      // Remove the tire from the local state
      onUpdateTires(prev => prev.filter(tire => tire.id !== tireId));

      // Remove the processing sum for this tire
      onUpdateProcessingSums(prev => {
        const newSums = { ...prev };
        delete newSums[tireId];
        return newSums;
      });

      // Show success message
      toast({
        title: "Tire Deleted",
        description: `Tire has been successfully removed.`
      });

      // If the deleted tire was selected, clear the selection
      if (selectedTireId === tireId) {
        onRowClick("");
      }
    } catch (error: any) {
      // Show error message
      toast({
        title: "Delete Failed",
        description: error?.message || "Error deleting tire.",
        variant: "destructive"
      });
    }
  };
  return (
    <section aria-labelledby="tires-table-heading" className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 id="tires-table-heading" className="text-xl font-semibold">
          {tires.length} Request Details
        </h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={onProcessingType} aria-label="Processing type">
            ZORRO
          </Button>
          {/* New tire management button */}
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsTireManagementDialogOpen(true)}
            className="text-blue-600 border-blue-600 hover:bg-blue-50 hover:text-blue-700"
            aria-label="Manage tires"
          >
            <ListFilter className="h-5 w-5" />
          </Button>
          <Button variant="outline" size="icon" onClick={onCopyTire} aria-label="Copy selected tire" disabled={!selectedTireId}>
            <Copy className="h-5 w-5" />
          </Button>
        </div>
      </div>
      <div className="rounded-md bg-card">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tug#</TableHead>
              <TableHead>Section</TableHead>
              <TableHead>Project#</TableHead>
              <TableHead>Spec#</TableHead>
              <TableHead>Tire Size</TableHead>
              <TableHead>Pattern</TableHead>
              <TableHead>Note</TableHead>
              <TableHead>Disposition</TableHead>
              <TableHead>N° (Process.)</TableHead>
              <TableHead className="w-[120px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tires.map((tire) => {
              const displayQuantity = processingNSums[tire.id] ?? 0;
              return (
                <TableRow
                  key={tire.id}
                  data-state={selectedTireId === tire.id ? "selected" : ""}
                  onClick={() => onRowClick(tire.id)}
                  className={cn(
                    "cursor-pointer",
                    selectedTireId === tire.id ? "bg-primary/30" : "hover:bg-accent/30"
                  )}
                  aria-selected={selectedTireId === tire.id}
                >
                  <TableCell>{tire.tugNo}</TableCell>
                  <TableCell>{tire.section}</TableCell>
                  <TableCell>{tire.projectNo}</TableCell>
                  <TableCell>{tire.specNo}</TableCell>
                  <TableCell>{tire.tireSize}</TableCell>
                  <TableCell>{tire.pattern}</TableCell>
                  <TableCell>{tire.note ? (tire.note.length > 20 ? tire.note.substring(0, 20) + '...' : tire.note) : 'N/A'}</TableCell>
                  <TableCell><DispositionBadge disposition={tire.disposition} /></TableCell>
                  <TableCell>{displayQuantity}</TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      {onManageCuts && (
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            onManageCuts(tire);
                          }}
                          title="Gestisci Tagli"
                          className="h-8 w-8"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      )}
                      <DeleteButton
                        onClick={() => handleDeleteTire(tire.id)}
                        title="Confirm Tire Deletion"
                        description={`Are you sure you want to delete this tire? This action cannot be undone.`}
                        itemType="tire"
                        itemName={tire.tugNo}
                        size="icon"
                        className="h-8 w-8"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {tires.length === 0 && (
              <TableRow>
                <TableCell colSpan={10} className="text-center">
                  No tires found for this request.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Tire Management Dialog */}
      <TireManagementDialog
        open={isTireManagementDialogOpen}
        onOpenChange={setIsTireManagementDialogOpen}
        onAddSelectedTires={handleAddTiresFromManagement}
      />
    </section>
  );
}
